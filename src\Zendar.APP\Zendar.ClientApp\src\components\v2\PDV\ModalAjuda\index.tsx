import {
  ModalProps,
  ModalContent,
  ModalBody,
  Box,
  useDisclosure,
  useToken,
  Button,
  Flex,
  useMediaQuery,
} from '@chakra-ui/react';
import { create, InstanceProps } from 'react-modal-promise';

import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import { Ajuda } from 'pages/Ajuda';
import auth from 'modules/auth';

interface ModalAjudaProps
  extends Omit<ModalProps, 'children' | 'isOpen' | 'onClose'>,
    InstanceProps<ModalProps> {
  abrirChat: () => void;
}

export const ModalAjuda = create<ModalAjudaProps>(
  ({ onResolve, onReject, abrirChat, ...props }) => {
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });
    const clienteTipoSti3 = auth.obterClienteSTi3();
    const buttonColorScheme = useToken('colors', 'ajuda.buttonColorScheme');
    const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

    const fecharModalAbrirChatOnline = () => {
      onClose();
      abrirChat();
    };

    return (
      <ModalPadraoChakra
        isCentered={!isLargerThan900}
        size={isLargerThan900 ? '6xl' : 'full'}
        {...props}
        isOpen={isOpen}
        onClose={onClose}
      >
        <ModalContent
          bg="white"
          w={['full', 'full', 'full', '1024px']}
          overflowY="auto"
          h={['100vh', '100%', '100%', '500px']}
        >
          <ModalBody px={0} py={0} bg="white" borderRadius="5px">
            <Box background="ajuda.bg" h="12px" />
            <Box h={['fit-content', 'fit-content', 'fit-content', '400px']}>
              <Ajuda exibirChat={false} />
            </Box>
            <Flex
              w="full"
              justify="center"
              flexDir={['column', 'row-reverse']}
              mb={['80px', '0px']}
              px="24px"
              gap="12px"
              pt={['24px', '24px', '24px', '0px']}
              align="center"
            >
              {clienteTipoSti3 && (
                <Button
                  colorScheme={buttonColorScheme}
                  size="sm"
                  w="320px"
                  textDecor="underline"
                  onClick={fecharModalAbrirChatOnline}
                >
                  Iniciar chat de atendimento online →
                </Button>
              )}
              <Button
                w={['320px', '120px']}
                variant="outlineDefault"
                colorScheme="gray"
                size="sm"
                onClick={onClose}
              >
                Fechar
              </Button>
            </Flex>
          </ModalBody>
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);
