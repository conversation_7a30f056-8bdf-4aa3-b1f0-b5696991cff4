import { useState, useCallback } from 'react';

import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';

import api, { ResponseApi } from 'services/api';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import { LoadMoreRowsParams } from 'components/update/Table/VirtualizedInfiniteTable';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import TipoProdutoEnum, { TipoProduto } from 'constants/enum/tipoProduto';

import { ModalVincularProduto } from '../ModalVincularProduto';

export enum EntradaMercadoriaStatusVinculoProduto {
  NAO_VINCULADO = 'NAO_VINCULADO',
  PENDENTE_INFORMAR_VARIACOES = 'PENDENTE_INFORMAR_VARIACOES',
  VINCULADO = 'VINCULADO',
}

export type InformacoesRodape = {
  totalProdutos: number;
  quantidadeItens: number;
  valorTotalProdutos: number;
  todosProdutosVinculados: boolean;
};

export type Produto = {
  isOpen: boolean;
  documentoFiscalItemId: string;
  descricaoProdutoNota: string;
  statusVinculo: EntradaMercadoriaStatusVinculoProduto;
  dadosAdicionais: string | null;
  quantidade: number;
  valorUnitario: number;
  valorTotal: number;
  cfopEntrada: string;
  cfopNota: string;
  ncm: string;
  codigoCest: string;
  produtoVinculado: {
    id: string;
    nome: null | string;
    conversao: number;
    novaQuantidade: number;
    codigoBarrasNota: string | null;
    codigoBarrasCadastro: string | null;
    dadosAdicionais: string | null;
    tipoProduto: number;
    volumeUnitario: boolean;
    referencia: string | null;
    precoCompra: number;
  } | null;
};

type ProdutoPaginadoRetorno = {
  totalProdutos: number;
  totalItens: number;
  valorTotal: number;
  todosProdutosVinculados: boolean;
  registros: Produto[];
};

export function useProdutosVinculacao(entradaMercadoriaId: string | null) {
  const { casasDecimais } = usePadronizacaoContext();

  const [isLoading, setIsLoading] = useState(false);
  const [produtos, setProdutos] = useState<Produto[]>([]);
  const [informacoesRodape, setInformacoesRodape] = useState<InformacoesRodape>(
    {
      quantidadeItens: 0,
      totalProdutos: 0,
      valorTotalProdutos: 0,
      todosProdutosVinculados: false,
    }
  );

  const todosProdutosVinculados = produtos.every(
    (produto) =>
      produto.statusVinculo === EntradaMercadoriaStatusVinculoProduto.VINCULADO
  );

  const handleToggleLinhaProduto = useCallback((index: number) => {
    setProdutos((prev) => {
      const produtosAtualizados = [...prev];
      const produtoAtualizado = produtosAtualizados[index];

      produtosAtualizados.splice(index, 1, {
        ...produtoAtualizado,
        isOpen: !produtoAtualizado.isOpen,
      });

      return produtosAtualizados;
    });
  }, []);

  const encontrarProximoProdutoParaVincular = useCallback(
    (index: number, produtosAtualizados: Produto[]): number | null => {
      const produto = produtosAtualizados[index];

      if (
        produto &&
        produto.statusVinculo !==
          EntradaMercadoriaStatusVinculoProduto.VINCULADO
      ) {
        return index;
      } else if (index < produtosAtualizados.length - 1) {
        return encontrarProximoProdutoParaVincular(
          index + 1,
          produtosAtualizados
        );
      }
      return null;
    },
    []
  );

  const vincularProduto = useCallback(
    async (
      index: number,
      isEdicao = false,
      produtosAtualizados = produtos,
      produtoPendenteVariacoes?: ProdutoOptionProps
    ) => {
      const {
        documentoFiscalItemId,
        descricaoProdutoNota,
        quantidade,
        valorUnitario,
        valorTotal,
        cfopNota,
        cfopEntrada,
        codigoCest,
        ncm,
        produtoVinculado,
        dadosAdicionais,
        statusVinculo,
      } = produtosAtualizados[index];

      const obterCodigoBarras = () => {
        const produtoTipoVariacao =
          produtoVinculado?.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;

        if (produtoTipoVariacao) {
          return '';
        }

        if (produtoVinculado?.codigoBarrasCadastro) {
          return produtoVinculado?.codigoBarrasCadastro;
        }

        if (produtoVinculado?.codigoBarrasNota) {
          return produtoVinculado?.codigoBarrasNota;
        }

        return '';
      };

      if (entradaMercadoriaId) {
        try {
          const {
            success,
            quantidade: novaQuantidade,
            valorUnitario: novoValorUnitario,
            cfop: novoCfop,
            codigoBarrasCadastro: novoCodigoBarrasCadastro,
            temProximoProdutoParaVincular,
            produtoVinculado: novoProdutoVinculado,
          } = await ModalVincularProduto({
            produtoPendenteVariacoes,
            casasDecimaisQuantidade: casasDecimais.casasDecimaisQuantidade,
            casasDecimaisValor: casasDecimais.casasDecimaisValor,
            produto: {
              documentoFiscalItemId,
              descricaoProduto: descricaoProdutoNota,
              quantidade,
              valorUnitario,
              valorTotal,
              cfop: cfopEntrada,
              cfopNota,
              codigoGTINEAN: obterCodigoBarras(),
              codigoBarrasNota: produtoVinculado?.codigoBarrasNota || null,
              ncm,
              codigoCest,
              dadosAdicionais: dadosAdicionais ?? '',
              tipoProduto: (produtoVinculado?.tipoProduto ??
                TipoProdutoEnum.PRODUTO_SIMPLES) as TipoProduto,
            },
            produtoJaVinculado:
              statusVinculo === EntradaMercadoriaStatusVinculoProduto.VINCULADO,
            totalProdutos: informacoesRodape.totalProdutos,
            entradaMercadoriaId,
            isEdicao,
            numeroItem:
              produtosAtualizados?.filter(
                ({ statusVinculo: statusVinculoProduto }) =>
                  statusVinculoProduto ===
                  EntradaMercadoriaStatusVinculoProduto.VINCULADO
              )?.length + 1,
            proximoItem: index + 1,
          });

          if (success) {
            const novosProdutos = [...produtosAtualizados];
            const produtoParaAtualizar = novosProdutos[index];

            novosProdutos.splice(index, 1, {
              ...produtoParaAtualizar,
              statusVinculo: EntradaMercadoriaStatusVinculoProduto.VINCULADO,
              valorUnitario: novoValorUnitario,
              cfopEntrada: novoCfop,
              produtoVinculado: {
                ...produtoParaAtualizar.produtoVinculado,
                nome:
                  novoProdutoVinculado?.nome ||
                  produtoParaAtualizar.produtoVinculado?.nome,
                tipoProduto:
                  novoProdutoVinculado?.tipoProduto ||
                  produtoParaAtualizar.produtoVinculado?.tipoProduto,
                codigoBarrasCadastro: novoCodigoBarrasCadastro,
                quantidade: novaQuantidade,
                conversao: parseFloat(
                  (novaQuantidade / produtoParaAtualizar.quantidade).toFixed(4)
                ),
                novaQuantidade,
              } as any,
            });

            setProdutos(novosProdutos);
          }

          if (temProximoProdutoParaVincular) {
            const proximoIndex = encontrarProximoProdutoParaVincular(
              index + 1,
              produtosAtualizados
            );
            if (proximoIndex !== null) {
              await vincularProduto(proximoIndex, false, produtosAtualizados);
            }
          }
          return success;
        } catch (error) {
          return error;
        }
      }

      return false;
    },
    [
      produtos,
      entradaMercadoriaId,
      casasDecimais,
      informacoesRodape.totalProdutos,
      encontrarProximoProdutoParaVincular,
    ]
  );

  const handleEditar = useCallback(
    async (index: number) => {
      await vincularProduto(index, true);
    },
    [vincularProduto]
  );

  const handleVincularProduto = useCallback(
    async (index: number, produtoPendenteVariacoes?: ProdutoOptionProps) => {
      await vincularProduto(index, false, produtos, produtoPendenteVariacoes);
    },
    [vincularProduto, produtos]
  );

  const loadMoreRows = useCallback(async () => {
    if (entradaMercadoriaId) {
      setIsLoading(true);

      const response = await api.get<void, ResponseApi<ProdutoPaginadoRetorno>>(
        ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_LISTAR_ITENS_PAGINADOS_IMPORTACAO_XML,
        {
          params: {
            id: entradaMercadoriaId,
            paginaAtual: 1,
            tamanhoPagina: 50,
            campoOrdenacao: 'NomeProduto',
            direcaoOrdenacao: 'ASC',
          },
        }
      );

      if (response) {
        if (response.sucesso) {
          setProdutos((prev) => [
            ...prev,
            ...(response.dados.registros || []).map(
              (registro) =>
                ({
                  ...registro,
                  isOpen: false,
                } as Produto)
            ),
          ]);

          setInformacoesRodape({
            quantidadeItens: response.dados.totalItens,
            totalProdutos: response.dados.totalProdutos,
            valorTotalProdutos: response.dados.valorTotal,
            todosProdutosVinculados: response.dados.todosProdutosVinculados,
          });
        }
      }
    }

    setIsLoading(false);
  }, [entradaMercadoriaId]);

  return {
    produtos,
    informacoesRodape,
    todosProdutosVinculados,
    isLoading,
    handleToggleLinhaProduto,
    handleEditar,
    handleVincularProduto,
    loadMoreRows,
  };
}
