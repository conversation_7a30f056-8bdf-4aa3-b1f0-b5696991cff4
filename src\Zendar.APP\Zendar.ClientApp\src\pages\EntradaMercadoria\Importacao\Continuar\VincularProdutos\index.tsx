import {
  Button,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>con<PERSON>utton,
  <PERSON>ack,
  Tooltip,
  useDisclosure,
  useMediaQuery,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  AccordionIcon,
} from '@chakra-ui/react';
import { FiMaximize2, FiMinimize2 } from 'react-icons/fi';
import { toast } from 'react-toastify';

import { useEntradaMercadoriaDadosCadastroContext } from 'store/EntradaMercadoria/EntradaMercadoriaDadosCadastro';
import { useEntradaMercadoriaEtapasContext } from 'store/EntradaMercadoria/EntradaMercadoriaEtapas';

import {
  Container,
  Body,
  Footer,
  StepDescriptionAccordion,
} from 'components/update/Steps/StepContent';

import { ListagemProdutos } from './components/ListagemProdutos';
import TotalizadoresFixos from './components/TotalizadoresFixos';
import { useProdutosVinculacao } from './hooks/useProdutosVinculacao';

export function VincularProd<PERSON>s() {
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const { isOpen, onOpen, onClose } = useDisclosure();

  const { nextStep, previousStep } = useEntradaMercadoriaEtapasContext();
  const {
    entradaMercadoriaId,
    descartarEntradaMercadoria,
    voltarParaListagem,
    temPermissaoExcluir,
    isReadOnly,
    menuIsOpen,
  } = useEntradaMercadoriaDadosCadastroContext();

  const {
    produtos,
    todosProdutosVinculados,
    isLoading,
    informacoesRodape,
    handleToggleLinhaProduto,
    handleEditar,
    handleVincularProduto,
    loadMoreRows,
  } = useProdutosVinculacao(entradaMercadoriaId ?? null);

  function handleDescartarEntradaMercadoria() {
    descartarEntradaMercadoria();
  }

  function handleVoltar() {
    previousStep();
  }

  function handleSalvarRascunho() {
    voltarParaListagem();
  }

  function handleAvancar() {
    if (todosProdutosVinculados) {
      nextStep();
    } else {
      toast.warning('É necessário vincular todos os produtos para continuar.');
    }
  }

  return (
    <>
      <Container mt="6px">
        <StepDescriptionAccordion
          stepNumber={2}
          title="Lista de produtos"
          description='Todos os produtos contidos na nota fiscal precisam ser vinculados a um produto existente no sistema. Clique em "vincular ao sistema" em cada um dos itens listados abaixo para realizar esta ação. Caso exista um novo produto você poderá cadastrá-lo na própria tela de vinculação.'
          customIcon={
            <HStack spacing={3}>
              <Tooltip label="Exibir listagem em tela cheia">
                <IconButton
                  aria-label="Maximizar listagem"
                  size="sm"
                  variant="ghost"
                  color="secondary.300"
                  icon={<FiMaximize2 />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onOpen();
                  }}
                />
              </Tooltip>
              <AccordionIcon color="secondary.300" fontSize="xl" />
            </HStack>
          }
        />
        {!isOpen && (
          <Body>
            <ListagemProdutos
              produtos={produtos}
              informacoesRodape={informacoesRodape}
              isLoading={isLoading}
              handleToggleLinhaProduto={handleToggleLinhaProduto}
              handleEditar={handleEditar}
              handleVincularProduto={handleVincularProduto}
              entradaMercadoriaId={entradaMercadoriaId ?? null}
            />
          </Body>
        )}
      </Container>
      {informacoesRodape.totalProdutos > 0 && (
        <TotalizadoresFixos
          quantidadeItens={informacoesRodape.quantidadeItens}
          totalProdutos={informacoesRodape.totalProdutos}
          valorTotalProdutos={informacoesRodape.valorTotalProdutos}
        />
      )}
      <Footer
        justifyContent="space-between"
        position={isLargerThan900 ? 'fixed' : 'relative'}
        bottom="0px"
        bg="gray.50"
        borderTop={isLargerThan900 ? '1px solid' : 'none'}
        borderColor="#5502B2"
        w={`calc(100% - ${menuIsOpen ? '210px' : '108px'})`}
        py="16px"
        px="48px"
      >
        <Button
          variant="outlineDefault"
          borderRadius="full"
          w="full"
          maxW={{ base: 'full', md: '160px' }}
          onClick={handleVoltar}
        >
          Voltar
        </Button>
        <Stack
          w="full"
          justifyContent="flex-end"
          direction={{ base: 'column', md: 'row' }}
          spacing={{ base: 2, sm: 4, md: 6 }}
        >
          {isReadOnly ? (
            <Button
              variant="outlineDefault"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '196px' }}
              onClick={voltarParaListagem}
            >
              Voltar para a listagem
            </Button>
          ) : (
            <Button
              variant="outlineDefault"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '160px' }}
              onClick={handleDescartarEntradaMercadoria}
              isDisabled={!temPermissaoExcluir}
            >
              Descartar
            </Button>
          )}
          {!isReadOnly && (
            <Button
              variant="outlineDefault"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '160px' }}
              onClick={handleSalvarRascunho}
            >
              Salvar e sair
            </Button>
          )}
          <Button
            colorScheme="purple"
            borderRadius="full"
            w="full"
            maxW={{ base: 'full', md: '160px' }}
            onClick={handleAvancar}
            isDisabled={!todosProdutosVinculados}
          >
            Avançar
          </Button>
        </Stack>
      </Footer>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size="full"
        scrollBehavior="inside"
      >
        <ModalContent
          bg="gray.50"
          h="100vh"
          display="flex"
          flexDirection="column"
        >
          <ModalHeader
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            Lista de produtos
            <IconButton
              aria-label="Minimizar listagem"
              borderRadius="4px"
              bg="transparent"
              icon={<FiMinimize2 />}
              onClick={onClose}
            />
          </ModalHeader>
          <ModalBody p={0} overflow="hidden">
            <ListagemProdutos
              produtos={produtos}
              informacoesRodape={informacoesRodape}
              isLoading={isLoading}
              handleToggleLinhaProduto={handleToggleLinhaProduto}
              handleEditar={handleEditar}
              handleVincularProduto={handleVincularProduto}
              modoTelaCheia
              entradaMercadoriaId={entradaMercadoriaId ?? null}
            />
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}
